import { createApi } from '@reduxjs/toolkit/query/react'
import axiosBaseQuery from '~/lib/redux/helpers'
import { WorkspaceListResType } from '~/schemas/workspace.schema'
import { CommonQueryParams } from '~/types/query-params.type'

const WORKSPACE_API_URL = '/workspaces' as const

const reducerPath = 'workspace/api' as const
const tagTypes = ['Workspace'] as const

export const workspaceApi = createApi({
  reducerPath,
  tagTypes,
  baseQuery: axiosBaseQuery(),
  endpoints: (build) => ({
    getWorkspaces: build.query<WorkspaceListResType, CommonQueryParams>({
      query: (params) => ({ url: WORKSPACE_API_URL, method: 'GET', params }),
      providesTags: (result) =>
        result
          ? [
              ...result.result.workspaces.map(({ _id }) => ({ type: 'Workspace' as const, id: _id })),
              { type: 'Workspace' as const, id: 'LIST' }
            ]
          : [{ type: 'Workspace' as const, id: 'LIST' }]
    })
  })
})

export const { useGetWorkspacesQuery } = workspaceApi

const workspaceApiReducer = workspaceApi.reducer

export default workspaceApiReducer
